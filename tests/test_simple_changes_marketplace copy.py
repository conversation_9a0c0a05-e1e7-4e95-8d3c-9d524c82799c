"""
Simple test to verify is_changes_marketplace logic is working correctly.
"""

import sys
import os

# Add the current directory to the path so we can import the app modules
sys.path.append(os.getcwd())

from app.schemas.workflow import WorkflowInDB, WorkflowPatchPayload


def test_workflow_schema_includes_is_changes_marketplace():
    """Test that workflow schema includes is_changes_marketplace field"""
    
    # Test that the field is included in the response schema
    workflow_data = {
        "id": "test-id",
        "name": "Test Workflow",
        "workflow_url": "http://example.com",
        "builder_url": "http://example.com",
        "start_nodes": [],
        "owner_id": "user-123",
        "user_ids": ["user-123"],
        "owner_type": "user",
        "version": "1.0.0",
        "is_changes_marketplace": True
    }
    
    workflow = WorkflowInDB.model_validate(workflow_data)
    assert workflow.is_changes_marketplace == True
    print(f"✅ WorkflowInDB.is_changes_marketplace = {workflow.is_changes_marketplace}")
    
    # Test that the field can be updated via PATCH
    patch_data = {"is_changes_marketplace": False}
    patch_payload = WorkflowPatchPayload.model_validate(patch_data)
    assert patch_payload.is_changes_marketplace == False
    print(f"✅ WorkflowPatchPayload.is_changes_marketplace = {patch_payload.is_changes_marketplace}")


def test_workflow_schema_defaults():
    """Test that workflow schema has correct defaults"""
    
    # Test minimal workflow data
    minimal_data = {
        "name": "Test Workflow",
        "workflow_url": "http://example.com",
        "builder_url": "http://example.com",
        "start_nodes": [],
        "owner_id": "user-123",
        "user_ids": ["user-123"],
        "owner_type": "user",
        "version": "1.0.0"
    }
    
    workflow = WorkflowInDB.model_validate(minimal_data)
    
    # Check defaults
    print(f"is_changes_marketplace default: {workflow.is_changes_marketplace}")
    print(f"is_customizable default: {workflow.is_customizable}")
    print(f"auto_version_on_update default: {workflow.auto_version_on_update}")
    
    # These should all be False by default
    assert workflow.is_changes_marketplace == False
    assert workflow.is_customizable == False
    assert workflow.auto_version_on_update == False
    
    print("✅ All workflow schema defaults are correct!")


def test_patch_payload_optional_fields():
    """Test that patch payload handles optional fields correctly"""
    
    # Test with only is_changes_marketplace
    patch1 = WorkflowPatchPayload.model_validate({"is_changes_marketplace": True})
    assert patch1.is_changes_marketplace == True
    assert patch1.is_customizable is None
    assert patch1.auto_version_on_update is None
    
    # Test with multiple fields
    patch2 = WorkflowPatchPayload.model_validate({
        "is_changes_marketplace": False,
        "is_customizable": True,
        "auto_version_on_update": True,
        "name": "Updated Name"
    })
    assert patch2.is_changes_marketplace == False
    assert patch2.is_customizable == True
    assert patch2.auto_version_on_update == True
    assert patch2.name == "Updated Name"
    
    print("✅ Patch payload optional fields work correctly!")


if __name__ == "__main__":
    print("Testing is_changes_marketplace schema logic...")
    test_workflow_schema_includes_is_changes_marketplace()
    test_workflow_schema_defaults()
    test_patch_payload_optional_fields()
    print("🎉 All tests passed!")
