"""
Test cases for auto-versioning functionality to ensure the fix works correctly.
"""

import pytest
import uuid
import sys
import os
from unittest.mock import Mock, patch

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.services.workflow_functions import WorkflowFunctions
from app.grpc_ import workflow_pb2
from app.models.workflow import Workflow, WorkflowVersion
from app.utils.constants.constants import (
    WorkflowStatusEnum,
    WorkflowVisibilityEnum,
    WorkflowOwnerTypeEnum,
    WorkflowCategoryEnum
)
from app.db.session import SessionLocal


@pytest.fixture
def db_session():
    """Create a database session for testing"""
    db = SessionLocal()
    try:
        yield db
    finally:
        # Clean up any test data
        try:
            db.rollback()
        except:
            pass
        db.close()


class TestAutoVersioningFunctionality:
    """Test class for auto-versioning functionality"""

    def setup_method(self):
        """Setup for each test method"""
        self.workflow_functions = WorkflowFunctions()
        self.test_workflow_id = f"test-workflow-{uuid.uuid4()}"
        self.test_user_id = f"test-user-{uuid.uuid4()}"

    def test_auto_versioning_enabled_creates_new_version(self, db_session):
        """Test that auto-versioning creates a new version when enabled"""
        
        # Create a test workflow with auto_version_on_update=True
        test_workflow = Workflow(
            id=self.test_workflow_id,
            name="Test Auto Versioning",
            description="Original description",
            workflow_url="https://example.com/workflow.json",
            builder_url="https://example.com/builder.json",
            start_nodes=[{"id": "start", "type": "start"}],
            owner_id=self.test_user_id,
            owner_type=WorkflowOwnerTypeEnum.USER,
            auto_version_on_update=True,  # Enable auto-versioning
            visibility=WorkflowVisibilityEnum.PRIVATE,
            status=WorkflowStatusEnum.ACTIVE,
            category=WorkflowCategoryEnum.AUTOMATION,
            tags=["test"]
        )
        
        db_session.add(test_workflow)
        db_session.flush()
        
        # Create initial version
        initial_version = WorkflowVersion(
            workflow_id=test_workflow.id,
            version_number="1.0.0",
            name=test_workflow.name,
            description=test_workflow.description,
            workflow_url=test_workflow.workflow_url,
            builder_url=test_workflow.builder_url,
            start_nodes=test_workflow.start_nodes,
            category=test_workflow.category,
            tags=test_workflow.tags,
            changelog="Initial version"
        )
        
        db_session.add(initial_version)
        db_session.flush()
        
        test_workflow.current_version_id = initial_version.id
        db_session.commit()
        
        # Create update request with version-relevant changes
        update_request = workflow_pb2.UpdateWorkflowRequest()
        update_request.id = test_workflow.id
        update_request.name = "Updated Auto Versioning Test"
        update_request.description = "Updated description"
        update_request.owner.id = self.test_user_id
        
        update_request.update_mask.paths.append("name")
        update_request.update_mask.paths.append("description")
        
        context = Mock()
        
        # Mock get_db to return our test session
        with patch.object(self.workflow_functions, 'get_db', return_value=db_session):
            response = self.workflow_functions.updateWorkflow(update_request, context)
        
        # Verify update was successful
        assert response.success is True
        assert "created new version" in response.message
        
        # Verify new version was created
        versions = db_session.query(WorkflowVersion).filter(
            WorkflowVersion.workflow_id == test_workflow.id
        ).order_by(WorkflowVersion.created_at.desc()).all()
        
        assert len(versions) == 2
        
        # Verify latest version has updated content
        latest_version = versions[0]
        assert latest_version.version_number == "1.1.0"
        assert latest_version.name == "Updated Auto Versioning Test"
        assert latest_version.description == "Updated description"
        
        # Verify workflow points to new version
        db_session.refresh(test_workflow)
        assert test_workflow.current_version_id == latest_version.id

    def test_list_versions_shows_latest_changes(self, db_session):
        """Test that listVersions shows the latest changes after auto-versioning"""
        
        # Create workflow and initial version (similar to above)
        test_workflow = Workflow(
            id=self.test_workflow_id,
            name="Test List Versions",
            description="Original description",
            workflow_url="https://example.com/workflow.json",
            builder_url="https://example.com/builder.json",
            start_nodes=[{"id": "start", "type": "start"}],
            owner_id=self.test_user_id,
            owner_type=WorkflowOwnerTypeEnum.USER,
            auto_version_on_update=True,
            visibility=WorkflowVisibilityEnum.PRIVATE,
            status=WorkflowStatusEnum.ACTIVE,
            category=WorkflowCategoryEnum.AUTOMATION,
            tags=["test"]
        )
        
        db_session.add(test_workflow)
        db_session.flush()
        
        initial_version = WorkflowVersion(
            workflow_id=test_workflow.id,
            version_number="1.0.0",
            name=test_workflow.name,
            description=test_workflow.description,
            workflow_url=test_workflow.workflow_url,
            builder_url=test_workflow.builder_url,
            start_nodes=test_workflow.start_nodes,
            category=test_workflow.category,
            tags=test_workflow.tags,
            changelog="Initial version"
        )
        
        db_session.add(initial_version)
        db_session.flush()
        
        test_workflow.current_version_id = initial_version.id
        db_session.commit()
        
        # Update workflow to trigger auto-versioning
        update_request = workflow_pb2.UpdateWorkflowRequest()
        update_request.id = test_workflow.id
        update_request.name = "Updated List Versions Test"
        update_request.description = "Updated description for list test"
        update_request.owner.id = self.test_user_id
        
        update_request.update_mask.paths.append("name")
        update_request.update_mask.paths.append("description")
        
        context = Mock()
        
        with patch.object(self.workflow_functions, 'get_db', return_value=db_session):
            # Update workflow
            update_response = self.workflow_functions.updateWorkflow(update_request, context)
            assert update_response.success is True
            
            # Test listVersions
            list_request = workflow_pb2.ListWorkflowVersionsRequest()
            list_request.workflow_id = test_workflow.id
            list_request.user_id = self.test_user_id
            list_request.page = 1
            list_request.page_size = 10
            
            list_response = self.workflow_functions.listWorkflowVersions(list_request, context)
        
        # Verify listVersions response
        assert list_response.success is True
        assert list_response.total == 2
        assert len(list_response.versions) == 2
        
        # Verify latest version is first (ordered by created_at desc)
        latest_version = list_response.versions[0]
        assert latest_version.version_number == "1.1.0"
        assert latest_version.name == "Updated List Versions Test"
        assert latest_version.description == "Updated description for list test"
        assert latest_version.is_current is True
        
        # Verify older version is second
        older_version = list_response.versions[1]
        assert older_version.version_number == "1.0.0"
        assert older_version.name == "Test List Versions"
        assert older_version.is_current is False

    def test_auto_versioning_disabled_no_new_version(self, db_session):
        """Test that no new version is created when auto-versioning is disabled"""
        
        # Create workflow with auto_version_on_update=False
        test_workflow = Workflow(
            id=self.test_workflow_id,
            name="Test No Auto Versioning",
            description="Original description",
            workflow_url="https://example.com/workflow.json",
            builder_url="https://example.com/builder.json",
            start_nodes=[{"id": "start", "type": "start"}],
            owner_id=self.test_user_id,
            owner_type=WorkflowOwnerTypeEnum.USER,
            auto_version_on_update=False,  # Disable auto-versioning
            visibility=WorkflowVisibilityEnum.PRIVATE,
            status=WorkflowStatusEnum.ACTIVE,
            category=WorkflowCategoryEnum.AUTOMATION,
            tags=["test"]
        )
        
        db_session.add(test_workflow)
        db_session.flush()
        
        initial_version = WorkflowVersion(
            workflow_id=test_workflow.id,
            version_number="1.0.0",
            name=test_workflow.name,
            description=test_workflow.description,
            workflow_url=test_workflow.workflow_url,
            builder_url=test_workflow.builder_url,
            start_nodes=test_workflow.start_nodes,
            category=test_workflow.category,
            tags=test_workflow.tags,
            changelog="Initial version"
        )
        
        db_session.add(initial_version)
        db_session.flush()
        
        test_workflow.current_version_id = initial_version.id
        db_session.commit()
        
        # Update workflow
        update_request = workflow_pb2.UpdateWorkflowRequest()
        update_request.id = test_workflow.id
        update_request.name = "Updated No Auto Versioning"
        update_request.description = "Updated description"
        update_request.owner.id = self.test_user_id
        
        update_request.update_mask.paths.append("name")
        update_request.update_mask.paths.append("description")
        
        context = Mock()
        
        with patch.object(self.workflow_functions, 'get_db', return_value=db_session):
            response = self.workflow_functions.updateWorkflow(update_request, context)
        
        # Verify update was successful but no new version created
        assert response.success is True
        assert "updated in place" in response.message
        
        # Verify only one version exists
        versions = db_session.query(WorkflowVersion).filter(
            WorkflowVersion.workflow_id == test_workflow.id
        ).all()
        
        assert len(versions) == 1
        
        # Verify the existing version was updated in place
        version = versions[0]
        assert version.version_number == "1.0.0"  # Version number unchanged
        assert version.name == "Updated No Auto Versioning"  # But content updated
        assert version.description == "Updated description"
