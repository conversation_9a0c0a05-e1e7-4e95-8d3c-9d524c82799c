# app/services/marketplace_functions.py
import grpc
import structlog
from datetime import datetime, timezone
from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.models.workflow import Workflow, WorkflowVersion, WorkflowMarketplaceListing
from app.grpc_ import workflow_pb2, workflow_pb2_grpc
from app.utils.constants.constants import WorkflowStatusEnum, WorkflowVisibilityEnum
from app.utils.kafka.kafka_service import KafkaProducer

# Initialize structured logger
logger = structlog.get_logger()


class WorkflowMarketplaceFunctions(workflow_pb2_grpc.WorkflowServiceServicer):
    def __init__(self):
        """Initialize the WorkflowService with a KafkaProducer instance"""
        self.kafka_producer = KafkaProducer()

    def get_db(self) -> Session:
        db = SessionLocal()
        try:
            return db
        finally:
            db.close()


    def getTemplate(
        self, request: workflow_pb2.GetTemplateRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.GetTemplateResponse:
        """
        Retrieve a marketplace listing by its ID (backward compatibility for template API).

        Args:
            request: The request containing the template ID (marketplace listing ID).
            context: The gRPC context for handling the request.

        Returns:
            Response containing the marketplace listing details if found.
        """
        db = self.get_db()
        logger.info("get_marketplace_listing_request", listing_id=request.id)
        try:
            # Get marketplace listing instead of template
            marketplace_listing = (
                db.query(WorkflowMarketplaceListing)
                .filter(WorkflowMarketplaceListing.id == request.id)
                .first()
            )
            if marketplace_listing is None:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Marketplace listing not found")
                return workflow_pb2.GetTemplateResponse(
                    success=False, message="Marketplace listing not found"
                )

            logger.info("marketplace_listing_retrieved", listing_id=marketplace_listing.id)

            # Convert marketplace listing to protobuf template format for backward compatibility
            template_proto = self._marketplace_listing_to_protobuf(marketplace_listing)

            # Check if user_id is provided and if the user has already used this marketplace listing
            is_added = False
            if request.HasField("user_id"):
                # Check if there are any workflows created from this marketplace listing by the user
                user_workflows = (
                    db.query(Workflow)
                    .filter(
                        Workflow.workflow_template_id == marketplace_listing.workflow_id,
                        Workflow.owner_id == request.user_id,
                    )
                    .first()
                )
                is_added = user_workflows is not None
                logger.info(
                    "checking_if_user_used_marketplace_listing",
                    user_id=request.user_id,
                    listing_id=marketplace_listing.id,
                    is_added=is_added,
                )

            # Set the is_added field
            template_proto.is_added = is_added

            print(f"[DEBUG] Marketplace listing retrieved: {template_proto}")
            return workflow_pb2.GetTemplateResponse(
                success=True,
                message=f"Marketplace listing {marketplace_listing.title} retrieved successfully",
                template=template_proto,
            )
        except Exception as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            logger.error("internal_server_error", error=e)
            return workflow_pb2.GetTemplateResponse(success=False, message="Internal Server Error")
        finally:
            db.close()

    def _marketplace_listing_to_protobuf(self, listing: WorkflowMarketplaceListing) -> workflow_pb2.WorkflowTemplate:
        """Convert a WorkflowMarketplaceListing model instance to a protobuf WorkflowTemplate message for backward compatibility."""
        return workflow_pb2.WorkflowTemplate(
            id=str(listing.id),
            name=listing.title,
            description=listing.description,
            workflow_url="",  # Will be populated from workflow version if needed
            builder_url="",   # Will be populated from workflow version if needed
            start_nodes=[],   # Will be populated from workflow version if needed
            use_count=listing.use_count,
            owner_id=listing.listed_by_user_id,
            execution_count=listing.execution_count,
            category=listing.category,
            tags=listing.tags if listing.tags else [],
            version="1.0.0",  # Default version for marketplace listings
            status=listing.status.value if hasattr(listing.status, 'value') else listing.status,
            created_at=listing.created_at.isoformat() if listing.created_at else "",
            updated_at=listing.updated_at.isoformat() if listing.updated_at else "",
        )


